# Production Parallel Bayesian LGC Model
# Optimized for longer runs with better convergence
#################

library(blavaan)
library(lavaan)
library(dplyr)
library(parallel)

# Enhanced parallel processing setup
cat("\n=== ENHANCED PARALLEL PROCESSING SETUP ===\n")
n_cores <- detectCores()
cat("Total CPU cores detected:", n_cores, "\n")

# For production runs, use more conservative core allocation
if(n_cores >= 8) {
  n_cores_use <- n_cores - 2  # Leave 2 cores free for system
} else if(n_cores >= 4) {
  n_cores_use <- n_cores - 1  # Leave 1 core free
} else {
  n_cores_use <- max(1, n_cores)  # Use all cores if <= 3
}

cat("Using", n_cores_use, "cores for production run\n")

# Set parallel options
options(mc.cores = n_cores_use)

# Production MCMC settings
PRODUCTION_CHAINS <- 4
PRODUCTION_BURNIN <- 2000
PRODUCTION_SAMPLES <- 5000
PRODUCTION_THIN <- 2

cat("\n=== PRODUCTION MCMC SETTINGS ===\n")
cat("Chains:", PRODUCTION_CHAINS, "\n")
cat("Burnin:", PRODUCTION_BURNIN, "\n") 
cat("Samples:", PRODUCTION_SAMPLES, "\n")
cat("Thinning:", PRODUCTION_THIN, "\n")
cat("Total iterations per chain:", PRODUCTION_BURNIN + PRODUCTION_SAMPLES * PRODUCTION_THIN, "\n")
cat("Effective samples:", PRODUCTION_SAMPLES * PRODUCTION_CHAINS, "\n\n")

# Load data
cat("=== LOADING DATA ===\n")
data_path <- "/home/<USER>/dissertation_folder/data"
longitudinal_complete <- read.csv(file.path(data_path, "longitudinal_data_self_control_withna.csv"))
cat("Dataset loaded - Sample size:", nrow(longitudinal_complete), "\n\n")

# Model specification (same as before)
meas_bayes <- '
SC3 =~ 1*sc3_task_completion + a2*sc3_distracted + a3*sc3_fidgeting +
       a4*sc3_think_act + a5*sc3_restless + a6*sc3_temper +
       a7*sc3_obedient + a8*sc3_lying
SC5 =~ 1*sc5_task_completion + a2*sc5_distracted + a3*sc5_fidgeting +
       a4*sc5_think_act + a5*sc5_restless + a6*sc5_temper +
       a7*sc5_obedient + a8*sc5_lying
SC7 =~ 1*sc7_task_completion + a2*sc7_distracted + a3*sc7_fidgeting +
       a4*sc7_think_act + a5*sc7_restless + a6*sc7_temper +
       a7*sc7_obedient + a8*sc7_lying
SC11 =~ 1*sc11_task_completion + a2*sc11_distracted + a3*sc11_fidgeting +
        a4*sc11_think_act + a5*sc11_restless + a6*sc11_temper +
        a7*sc11_obedient + a8*sc11_lying
SC14 =~ 1*sc14_task_completion + a2*sc14_distracted + a3*sc14_fidgeting +
        a4*sc14_think_act + a5*sc14_restless + a6*sc14_temper +
        a7*sc14_obedient + a8*sc14_lying
SC17 =~ 1*sc17_task_completion + a2*sc17_distracted + a3*sc17_fidgeting +
        a4*sc17_think_act + a5*sc17_restless + a6*sc17_temper +
        a7*sc17_obedient + a8*sc17_lying

# Partial scalar invariance
sc3_task_completion ~ 0*1
sc5_task_completion ~ 0*1
sc7_task_completion ~ 0*1
sc11_task_completion ~ 0*1
sc14_task_completion ~ 0*1
sc17_task_completion ~ 0*1

sc3_think_act ~ i4*1
sc5_think_act ~ i4*1
sc7_think_act ~ i4*1
sc11_think_act ~ i4*1
sc14_think_act ~ i4*1
sc17_think_act ~ i4*1

SC3 ~ 1
SC5 ~ 1
SC7 ~ 1
SC11 ~ 1
SC14 ~ 1
SC17 ~ 1
'

growth_bayes <- '
INTERCEPT =~ 1*SC3 + 1*SC5 + 1*SC7 + 1*SC11 + 1*SC14 + 1*SC17
SLOPE =~ 0*SC3 + 2*SC5 + 4*SC7 + 8*SC11 + 11*SC14 + 14*SC17

INTERCEPT ~~ INTERCEPT
SLOPE ~~ SLOPE
INTERCEPT ~~ SLOPE

INTERCEPT ~ 1
SLOPE ~ 1

SC3 ~~ v3*SC3
SC5 ~~ v5*SC5
SC7 ~~ v7*SC7
SC11 ~~ v11*SC11
SC14 ~~ v14*SC14
SC17 ~~ v17*SC17
'

model_bayes <- paste(meas_bayes, growth_bayes, sep = "\n")

# Production model fitting with enhanced monitoring
cat("=== PRODUCTION BAYESIAN MODEL FITTING ===\n")
start_time <- Sys.time()
cat("Starting production run at:", format(start_time), "\n")
cat("Estimated completion time:", format(start_time + as.difftime(30, units="mins")), "\n\n")

# Fit production model
fit_production <- bsem(model_bayes, 
                      data = longitudinal_complete,
                      meanstructure = TRUE,
                      n.chains = PRODUCTION_CHAINS,
                      burnin = PRODUCTION_BURNIN,
                      sample = PRODUCTION_SAMPLES,
                      thin = PRODUCTION_THIN,
                      mcmcfile = TRUE,
                      target = "stan",
                      cores = n_cores_use,
                      # Additional Stan options for better performance
                      bcontrol = list(adapt_delta = 0.95,
                                     max_treedepth = 12))

end_time <- Sys.time()
elapsed_time <- end_time - start_time
cat("\n=== PRODUCTION RUN COMPLETED ===\n")
cat("Completion time:", format(end_time), "\n")
cat("Total elapsed time:", round(elapsed_time, 2), attr(elapsed_time, "units"), "\n")
cat("Average time per chain:", round(elapsed_time/PRODUCTION_CHAINS, 2), attr(elapsed_time, "units"), "\n\n")

# Comprehensive diagnostics
cat("=== PRODUCTION MODEL DIAGNOSTICS ===\n")
summary(fit_production)

# Save results
save(fit_production, file = "results/bayesian_lgc_production_results.RData")
cat("\nResults saved to: results/bayesian_lgc_production_results.RData\n")

cat("\n=== PRODUCTION RUN SUMMARY ===\n")
cat("This production run provides:\n")
cat("- High-quality posterior samples for publication\n")
cat("- Robust convergence diagnostics\n") 
cat("- Parallel efficiency using", n_cores_use, "cores\n")
cat("- Comprehensive model results\n")
