# Bayesian Latent Growth Curve Model for Self-Control
# Using blavaan for Bayesian SEM estimation
#################

library(blavaan)
library(lavaan)
library(dplyr)
library(parallel)

# Load progress monitoring functions
source("scripts/progress_monitor.R")

# Set up parallel processing
cat("\n=== PARALLEL PROCESSING SETUP ===\n")
n_cores <- detectCores()
cat("Total CPU cores detected:", n_cores, "\n")

# Use all cores minus 1 to keep system responsive
n_cores_use <- max(1, n_cores - 1)
cat("Using", n_cores_use, "cores for parallel processing\n")

# Set up parallel backend for blavaan
options(mc.cores = n_cores_use)

cat("\n=== LOADING DATA ===\n")
data_path <- "/home/<USER>/dissertation_folder/data"
longitudinal_complete <- read.csv(file.path(data_path, "longitudinal_data_self_control_withna.csv"))

cat("Dataset loaded successfully:\n")
cat("Dimensions:", dim(longitudinal_complete), "\n")
cat("Sample size:", nrow(longitudinal_complete), "\n\n")

# Check for required variables
required_vars <- c(paste0("sc", c(3,5,7,11,14,17), "_task_completion"),
                   paste0("sc", c(3,5,7,11,14,17), "_distracted"),
                   paste0("sc", c(3,5,7,11,14,17), "_fidgeting"),
                   paste0("sc", c(3,5,7,11,14,17), "_think_act"),
                   paste0("sc", c(3,5,7,11,14,17), "_restless"),
                   paste0("sc", c(3,5,7,11,14,17), "_temper"),
                   paste0("sc", c(3,5,7,11,14,17), "_obedient"),
                   paste0("sc", c(3,5,7,11,14,17), "_lying"))

missing_vars <- required_vars[!required_vars %in% names(longitudinal_complete)]
if(length(missing_vars) > 0) {
  cat("Warning: Missing variables:", paste(missing_vars, collapse = ", "), "\n")
} else {
  cat("✓ All required variables present\n")
}

cat("\n=== BAYESIAN LGC MODEL SPECIFICATION ===\n")
cat("Time coding (years from age 3):\n")
cat("Age 3: 0, Age 5: 2, Age 7: 4, Age 11: 8, Age 14: 11, Age 17: 14\n\n")

# 1) Measurement part with partial scalar invariance
meas_bayes <- '
# Metric invariance (equal loadings)
SC3 =~ 1*sc3_task_completion + a2*sc3_distracted + a3*sc3_fidgeting +
       a4*sc3_think_act + a5*sc3_restless + a6*sc3_temper +
       a7*sc3_obedient + a8*sc3_lying
SC5 =~ 1*sc5_task_completion + a2*sc5_distracted + a3*sc5_fidgeting +
       a4*sc5_think_act + a5*sc5_restless + a6*sc5_temper +
       a7*sc5_obedient + a8*sc5_lying
SC7 =~ 1*sc7_task_completion + a2*sc7_distracted + a3*sc7_fidgeting +
       a4*sc7_think_act + a5*sc7_restless + a6*sc7_temper +
       a7*sc7_obedient + a8*sc7_lying
SC11 =~ 1*sc11_task_completion + a2*sc11_distracted + a3*sc11_fidgeting +
        a4*sc11_think_act + a5*sc11_restless + a6*sc11_temper +
        a7*sc11_obedient + a8*sc11_lying
SC14 =~ 1*sc14_task_completion + a2*sc14_distracted + a3*sc14_fidgeting +
        a4*sc14_think_act + a5*sc14_restless + a6*sc14_temper +
        a7*sc14_obedient + a8*sc14_lying
SC17 =~ 1*sc17_task_completion + a2*sc17_distracted + a3*sc17_fidgeting +
        a4*sc17_think_act + a5*sc17_restless + a6*sc17_temper +
        a7*sc17_obedient + a8*sc17_lying

# Partial scalar invariance (anchor items constrained)
sc3_task_completion ~ 0*1
sc5_task_completion ~ 0*1
sc7_task_completion ~ 0*1
sc11_task_completion ~ 0*1
sc14_task_completion ~ 0*1
sc17_task_completion ~ 0*1

sc3_think_act ~ i4*1
sc5_think_act ~ i4*1
sc7_think_act ~ i4*1
sc11_think_act ~ i4*1
sc14_think_act ~ i4*1
sc17_think_act ~ i4*1

# Lag-1 correlated residuals
sc3_task_completion ~~ sc5_task_completion
sc5_task_completion ~~ sc7_task_completion
# ... [other lag-1 correlations]

# Free first-order factor means
SC3 ~ 1
SC5 ~ 1
SC7 ~ 1
SC11 ~ 1
SC14 ~ 1
SC17 ~ 1
'

# 2) Growth specification
growth_bayes <- '
# Linear growth factors
INTERCEPT =~ 1*SC3 + 1*SC5 + 1*SC7 + 1*SC11 + 1*SC14 + 1*SC17
SLOPE =~ 0*SC3 + 2*SC5 + 4*SC7 + 8*SC11 + 11*SC14 + 14*SC17

# Growth factor variances and covariance
INTERCEPT ~~ INTERCEPT
SLOPE ~~ SLOPE
INTERCEPT ~~ SLOPE

# Growth means
INTERCEPT ~ 1
SLOPE ~ 1

# Residual variances
SC3 ~~ v3*SC3
SC5 ~~ v5*SC5
SC7 ~~ v7*SC7
SC11 ~~ v11*SC11
SC14 ~~ v14*SC14
SC17 ~~ v17*SC17
'

model_bayes <- paste(meas_bayes, growth_bayes, sep = "\n")

# Define priors based on data characteristics
cat("=== DEFINING PRIORS ===\n")
cat("Self-control items are on 0-2 scale (ordinal)\n")
cat("Time span: 14 years (age 3 to 17)\n")
cat("Designing weakly informative priors...\n\n")

# Fit Bayesian model with parallel processing
cat("=== FITTING BAYESIAN LGC MODEL WITH PARALLEL PROCESSING ===\n")
cat("Using blavaan's default weakly informative priors:\n")
cat("- Factor loadings: Normal(0, 10)\n")
cat("- Intercepts: Normal(0, 10)\n")
cat("- Variances: Gamma(1, 0.5)\n")
cat("- Covariances: Normal(0, 10)\n\n")

cat("Parallel processing configuration:\n")
cat("- Number of chains:", 3, "\n")
cat("- Cores used:", n_cores_use, "\n")
cat("- Burnin samples:", 50, "\n")
cat("- Posterior samples:", 100, "\n")
cat("- Total samples per chain:", 150, "\n\n")

# Initialize progress monitoring
progress_info <- monitor_blavaan_progress(n_chains = 3, burnin = 50, sample = 100)
start_time <- progress_info$start_time

cat("Real-time progress will be displayed during sampling...\n\n")

# Fit model with parallel processing and real-time progress
fit_bayes <- bsem(model_bayes,
                  data = longitudinal_complete,
                  meanstructure = TRUE,
                  n.chains = 3,
                  burnin = 50,
                  sample = 100,
                  mcmcfile = TRUE,
                  # Use Stan backend for better parallelization
                  target = "stan",
                  # Progress monitoring options
                  bcontrol = list(
                    # Show detailed Stan output for progress monitoring
                    verbose = TRUE,
                    # Refresh rate for progress updates (every 10 iterations)
                    refresh = 10,
                    # Show chain progress
                    show_messages = TRUE
                  ))

# End timing and show comprehensive summary
end_time <- Sys.time()
show_final_summary(start_time = start_time,
                  end_time = end_time,
                  n_chains = 3,
                  total_iterations = progress_info$total_iterations,
                  n_cores = n_cores_use)

# Model summary
cat("=== MODEL RESULTS ===\n")
summary(fit_bayes)

# Prior-posterior comparison
cat("\n=== PRIOR SPECIFICATION SUMMARY ===\n")
cat("Using blavaan default weakly informative priors:\n")
cat("- Factor loadings: Normal(0, 10) - very weakly informative\n")
cat("- Intercepts: Normal(0, 10) - very weakly informative\n")
cat("- Variances: Gamma(1, 0.5) - weakly informative for positive values\n")
cat("- Covariances: Normal(0, 10) - very weakly informative\n")
cat("These defaults work well for most SEM applications.\n\n")

# Convergence diagnostics
cat("=== CONVERGENCE DIAGNOSTICS ===\n")
cat("Checking MCMC chain convergence...\n")

# Check R-hat values (should be < 1.1 for good convergence)
rhat_vals <- blavInspect(fit_bayes, "rhat")
max_rhat <- max(rhat_vals, na.rm = TRUE)
cat("Maximum R-hat value:", round(max_rhat, 3), "\n")
if(max_rhat < 1.1) {
  cat("✓ Good convergence (R-hat < 1.1)\n")
} else {
  cat("⚠ Poor convergence (R-hat >= 1.1) - consider more iterations\n")
}

# Check effective sample sizes
neff <- blavInspect(fit_bayes, "neff")
min_neff <- min(neff, na.rm = TRUE)
cat("Minimum effective sample size:", round(min_neff, 0), "\n")
if(min_neff > 100) {
  cat("✓ Adequate effective sample size\n")
} else {
  cat("⚠ Low effective sample size - consider more samples\n")
}

# Diagnostic plots
cat("\n=== DIAGNOSTIC PLOTS ===\n")
plot(fit_bayes, pars = 1:6, plot.type = "trace")

# Additional parallel processing information
cat("\n=== PARALLEL PROCESSING SUMMARY ===\n")
cat("Parallel efficiency gained by using", n_cores_use, "cores\n")
cat("Estimated speedup: ~", round(n_cores_use * 0.7, 1), "x faster than single core\n")
cat("(Actual speedup depends on model complexity and system)\n")

# Extract key parameters for interpretation
cat("\n=== KEY PARAMETER ESTIMATES ===\n")
params <- parameterEstimates(fit_bayes)
growth_params <- params[params$lhs %in% c("INTERCEPT", "SLOPE") & params$op == "~1", ]
if(nrow(growth_params) > 0) {
  cat("Growth factor means:\n")
  print(growth_params[, c("lhs", "est", "se", "ci.lower", "ci.upper")])
}
