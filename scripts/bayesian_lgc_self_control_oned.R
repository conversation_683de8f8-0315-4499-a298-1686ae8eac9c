# Bayesian Latent Growth Curve Model for Self-Control
# Using blavaan for Bayesian SEM estimation
#################

library(blavaan)
library(lavaan)
library(dplyr)

cat("\n=== LOADING DATA ===\n")
data_path <- "/home/<USER>/dissertation_folder/data"
longitudinal_complete <- read.csv(file.path(data_path, "longitudinal_complete_self_control.csv"))

cat("Dataset loaded successfully:\n")
cat("Dimensions:", dim(longitudinal_complete), "\n")
cat("Sample size:", nrow(longitudinal_complete), "\n\n")

# Check for required variables
required_vars <- c(paste0("sc", c(3,5,7,11,14,17), "_task_completion"),
                   paste0("sc", c(3,5,7,11,14,17), "_distracted"),
                   paste0("sc", c(3,5,7,11,14,17), "_fidgeting"),
                   paste0("sc", c(3,5,7,11,14,17), "_think_act"),
                   paste0("sc", c(3,5,7,11,14,17), "_restless"),
                   paste0("sc", c(3,5,7,11,14,17), "_temper"),
                   paste0("sc", c(3,5,7,11,14,17), "_obedient"),
                   paste0("sc", c(3,5,7,11,14,17), "_lying"))

missing_vars <- required_vars[!required_vars %in% names(longitudinal_complete)]
if(length(missing_vars) > 0) {
  cat("Warning: Missing variables:", paste(missing_vars, collapse = ", "), "\n")
} else {
  cat("✓ All required variables present\n")
}

cat("\n=== BAYESIAN LGC MODEL SPECIFICATION ===\n")
cat("Time coding (years from age 3):\n")
cat("Age 3: 0, Age 5: 2, Age 7: 4, Age 11: 8, Age 14: 11, Age 17: 14\n\n")

# 1) Measurement part with partial scalar invariance
meas_bayes <- '
# Metric invariance (equal loadings)
SC3 =~ 1*sc3_task_completion + a2*sc3_distracted + a3*sc3_fidgeting +
       a4*sc3_think_act + a5*sc3_restless + a6*sc3_temper +
       a7*sc3_obedient + a8*sc3_lying
SC5 =~ 1*sc5_task_completion + a2*sc5_distracted + a3*sc5_fidgeting +
       a4*sc5_think_act + a5*sc5_restless + a6*sc5_temper +
       a7*sc5_obedient + a8*sc5_lying
SC7 =~ 1*sc7_task_completion + a2*sc7_distracted + a3*sc7_fidgeting +
       a4*sc7_think_act + a5*sc7_restless + a6*sc7_temper +
       a7*sc7_obedient + a8*sc7_lying
SC11 =~ 1*sc11_task_completion + a2*sc11_distracted + a3*sc11_fidgeting +
        a4*sc11_think_act + a5*sc11_restless + a6*sc11_temper +
        a7*sc11_obedient + a8*sc11_lying
SC14 =~ 1*sc14_task_completion + a2*sc14_distracted + a3*sc14_fidgeting +
        a4*sc14_think_act + a5*sc14_restless + a6*sc14_temper +
        a7*sc14_obedient + a8*sc14_lying
SC17 =~ 1*sc17_task_completion + a2*sc17_distracted + a3*sc17_fidgeting +
        a4*sc17_think_act + a5*sc17_restless + a6*sc17_temper +
        a7*sc17_obedient + a8*sc17_lying

# Partial scalar invariance (anchor items constrained)
sc3_task_completion ~ 0*1
sc5_task_completion ~ 0*1
sc7_task_completion ~ 0*1
sc11_task_completion ~ 0*1
sc14_task_completion ~ 0*1
sc17_task_completion ~ 0*1

sc3_think_act ~ i4*1
sc5_think_act ~ i4*1
sc7_think_act ~ i4*1
sc11_think_act ~ i4*1
sc14_think_act ~ i4*1
sc17_think_act ~ i4*1

# Lag-1 correlated residuals
sc3_task_completion ~~ sc5_task_completion
sc5_task_completion ~~ sc7_task_completion
# ... [other lag-1 correlations]

# Free first-order factor means
SC3 ~ 1
SC5 ~ 1
SC7 ~ 1
SC11 ~ 1
SC14 ~ 1
SC17 ~ 1
'

# 2) Growth specification
growth_bayes <- '
# Linear growth factors
INTERCEPT =~ 1*SC3 + 1*SC5 + 1*SC7 + 1*SC11 + 1*SC14 + 1*SC17
SLOPE =~ 0*SC3 + 2*SC5 + 4*SC7 + 8*SC11 + 11*SC14 + 14*SC17

# Growth factor variances and covariance
INTERCEPT ~~ INTERCEPT
SLOPE ~~ SLOPE
INTERCEPT ~~ SLOPE

# Growth means
INTERCEPT ~ 1
SLOPE ~ 1

# Residual variances
SC3 ~~ v3*SC3
SC5 ~~ v5*SC5
SC7 ~~ v7*SC7
SC11 ~~ v11*SC11
SC14 ~~ v14*SC14
SC17 ~~ v17*SC17
'

model_bayes <- paste(meas_bayes, growth_bayes, sep = "\n")

# Fit Bayesian model
cat("=== FITTING BAYESIAN LGC MODEL ===\n")
fit_bayes <- bsem(model_bayes, 
                  data = longitudinal_complete,
                  meanstructure = TRUE,
                  n.chains = 3,
                  burnin = 1000,
                  sample = 2000,
                  mcmcfile = TRUE)

# Model summary
summary(fit_bayes)
plot(fit_bayes, pars = 1:6, plot.type = "trace")
